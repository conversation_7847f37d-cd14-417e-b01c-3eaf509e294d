import 'order_product.dart';

{"status":0,"result":{"order_id":9133,"order_client_id":20,"order_client_name":"احمد عبد المولى","order_client_mobile":"777534555","order_client_whatsapp":"","order_client_image":"","order_driver_id":0,"order_driver_name":"","order_driver_mobile":"","order_driver_whatsapp":"","order_driver_image":"","order_member_addresse":"[اليمن] أمانة العاصمة - حديقة الحيوان , مسهست سنتس س","order_country_id":"235","order_country_name":"اليمن","order_country_code":"YEM","order_area_id":"474","order_area_name":"حديقة الحيوان","order_city_id":"4080","order_city_name":"أمانة العاصمة","order_address_id":50,"order_address":"مسهست سنتس س","order_address_type_id":1,"order_address_type_name":"منزل","order_latitude":15.3529,"order_longitude":44.188,"order_payment_id":1,"order_payment_name":"الدفع عند الاستلام","order_payment_icon":"https:\/\/platformdgtl.com\/castle\/upimages\/payment-ways\/icon2025-06-09-41758.png","order_payment_type":1,"order_payment_type_label":"بدون اي تفاصيل","order_payment_description":"","order_payment_account_number":"","order_payment_transfer_number":"","order_payment_transfer_company":"","order_payment_status":1,"order_status":1,"order_status_string":"طلب جديد","order_status_update_date":"18-06-2025 06:19ص","order_status_update_date_time":"06:19ص","order_delivery_type":0,"order_delivery_time_id":1,"order_delivery_time":"ud83dudef5  التوصيل الآن","order_delivery_time_label":"ud83dudef5  التوصيل الآن","order_delivery_date_day":"","order_delivery_from_hour_time":"00","order_delivery_to_hour_time":"00","order_date":"قبل 39 ثانية","order_notes":"","order_total_price":4500,"order_full_total_price":5500,"order_delivery_fees":1000,"order_delivery_fees_CurrencyVar":"1000 ريال","order_currency_id":3,"order_DeliveryFeesCurrencyId":3,"order_CurrencyName":"الريال اليمني","order_DeliveryFeesCurrencyName":"الريال اليمني","order_total_price_CurrencyVar":"4500 ريال","order_full_total_price_CurrencyVar":"5500 ريال","order_CurrencyVar":"ريال","order_items":[{"item_id":1,"item_name":"سلطة القلعة كبير","item_description":"الطعم الألذ","item_price":500,"item_total_price":4500,"item_price_CurrencyVar":"500 ريال","item_total_price_CurrencyVar":"4500 ريال","item_CurrencyVar":"ريال","item_quantity":9,"item_image":"https:\/\/platformdgtl.com\/castle\/upimages\/products\/pro2024-12-17-150152.png.webp","item_image_thumb":"https:\/\/platformdgtl.com\/castle\/upimages\/products\/thumbs\/pro2024-12-17-150152.png.webp"}]}}

class Order {
  Order({
    required this.orderId,
    required this.orderClientId,
    required this.orderClientName,
    required this.orderClientMobile,
    required this.orderClientWhatsapp,
    required this.orderDriverId,
    required this.orderDriverName,
    required this.orderDriverMobile,
    required this.orderDriverWhatsapp,
    required this.orderCountryId,
    required this.orderCountryName,
    required this.orderCountryCode,
    required this.orderAreaId,
    required this.orderAreaName,
    required this.orderCityId,
    required this.orderCityName,
    required this.orderAddressId,
    required this.orderAddress,
    required this.orderLatitude,
    required this.orderLongitude,
    required this.orderPaymentId,
    required this.orderPaymentName,
    required this.orderPaymentType,
    required this.orderPaymentTypeLabel,
    required this.orderPaymentDescription,
    required this.orderPaymentAccountNumber,
    required this.orderPaymentTransferNumber,
    required this.orderPaymentTransferCompany,
    required this.orderPaymentStatus,
    required this.orderStatus,
    required this.orderStatusString,
    required this.orderStatusUpdateDate,
    required this.orderDeliveryType,
    required this.orderDeliveryTimeId,
    required this.orderDeliveryTime,
    required this.orderDate,
    required this.orderNotes,
    required this.orderTotalPrice,
    required this.orderFullTotalPrice,
    required this.orderDeliveryFees,
    required this.products,
    required this.orderCurrencyVar,
    required this.orderCouponDiscountAmount,
    required this.orderCouponDiscountAmountCurrencyVar,
  });

  int orderId;
  int orderClientId;
  String orderClientName;
  String orderClientMobile;
  String orderClientWhatsapp;
  int orderDriverId;
  String orderDriverName;
  String orderDriverMobile;
  String orderDriverWhatsapp;
  String orderCountryId;
  String orderCountryName;
  String orderCountryCode;
  String orderAreaId;
  String orderAreaName;
  String orderCityId;
  String orderCityName;
  int orderAddressId;
  String orderAddress;
  double orderLatitude;
  double orderLongitude;
  int orderPaymentId;
  String orderPaymentName;
  int orderPaymentType;
  String orderPaymentTypeLabel;
  String orderPaymentDescription;
  String orderPaymentAccountNumber;
  String orderPaymentTransferNumber;
  String orderPaymentTransferCompany;
  int orderPaymentStatus;
  int orderStatus;
  String orderStatusString;
  String orderStatusUpdateDate;
  int orderDeliveryType;
  int orderDeliveryTimeId;
  String orderDeliveryTime;
  String orderDate;
  String orderNotes;
  double orderTotalPrice;
  double orderFullTotalPrice;
  double orderDeliveryFees;
  List<OrderProduct> products;

  double? orderCouponDiscountAmount;
  String? orderCouponDiscountAmountCurrencyVar;

  String? orderCurrencyVar;
  // from json
  factory Order.fromJson(Map<String, dynamic> json) {
    return Order(
      orderId: json['order_id'] as int,
      orderClientId: json['order_client_id'] as int,
      orderClientName: json['order_client_name'] as String,
      orderClientMobile: json['order_client_mobile'] as String,
      orderClientWhatsapp: json['order_client_whatsapp'] as String,
      orderDriverId: json['order_driver_id'] as int,
      orderDriverName: json['order_driver_name'] as String,
      orderDriverMobile: json['order_driver_mobile'] as String,
      orderDriverWhatsapp: json['order_driver_whatsapp'] as String,
      orderCountryId: json['order_country_id'] as String,
      orderCountryName: json['order_country_name'] as String,
      orderCountryCode: json['order_country_code'] as String,
      orderAreaId: json['order_area_id'] as String,
      orderAreaName: json['order_area_name'] as String,
      orderCityId: json['order_city_id'] as String,
      orderCityName: json['order_city_name'] as String,
      orderAddressId: json['order_address_id'] as int,
      orderAddress: json['order_address'] as String,
      orderLatitude: double.parse(json['order_latitude'].toString()),
      orderLongitude: double.parse(json['order_longitude'].toString()),
      orderPaymentId: json['order_payment_id'] as int,
      orderPaymentName: json['order_payment_name'] as String,
      orderPaymentType: json['order_payment_type'] as int,
      orderPaymentTypeLabel: json['order_payment_type_label'] as String,
      orderPaymentDescription: json['order_payment_description'] as String,
      orderPaymentAccountNumber: json['order_payment_account_number'] as String,
      orderPaymentTransferNumber:
          json['order_payment_transfer_number'] as String,
      orderPaymentTransferCompany:
          json['order_payment_transfer_company'] as String,
      orderPaymentStatus: json['order_payment_status'] as int,
      orderStatus: json['order_status'] as int,
      orderStatusString: json['order_status_string'] as String,
      orderStatusUpdateDate: json['order_status_update_date'] as String,
      orderDeliveryType: json['order_delivery_type'] as int,
      orderDeliveryTimeId: json['order_delivery_time_id'] as int,
      orderDeliveryTime: json['order_delivery_time'] as String,
      orderDate: json['order_date'] as String,
      orderNotes: json['order_notes'] as String,
      orderTotalPrice: double.parse(json['order_total_price'].toString()),
      orderFullTotalPrice:
          double.parse(json['order_full_total_price'].toString()),
      orderDeliveryFees: double.parse(json['order_delivery_fees'].toString()),
      products: (json['order_items'] as List)
          .map((e) => OrderProduct.fromJson(e as Map<String, dynamic>))
          .toList(),
      orderCurrencyVar: json['order_CurrencyVar'] as String?,
      orderCouponDiscountAmount: double.tryParse(
          json['order_coupon_discount_amount']?.toString() ?? ""),
      orderCouponDiscountAmountCurrencyVar:
          json['order_coupon_discount_amount_CurrencyVar']?.toString(),
    );
  }
  // to json
  Map<String, dynamic> toJson() {
    return {
      'order_id': orderId,
      'order_client_id': orderClientId,
      'order_client_name': orderClientName,
      'order_client_mobile': orderClientMobile,
      'order_client_whatsapp': orderClientWhatsapp,
      'order_driver_id': orderDriverId,
      'order_driver_name': orderDriverName,
      'order_driver_mobile': orderDriverMobile,
      'order_driver_whatsapp': orderDriverWhatsapp,
      'order_country_id': orderCountryId,
      'order_country_name': orderCountryName,
      'order_country_code': orderCountryCode,
      'order_area_id': orderAreaId,
      'order_area_name': orderAreaName,
      'order_city_id': orderCityId,
      'order_city_name': orderCityName,
      'order_address_id': orderAddressId,
      'order_address': orderAddress,
      'order_latitude': orderLatitude,
      'order_longitude': orderLongitude,
      'order_payment_id': orderPaymentId,
      'order_payment_name': orderPaymentName,
      'order_payment_type': orderPaymentType,
      'order_payment_type_label': orderPaymentTypeLabel,
      'order_payment_description': orderPaymentDescription,
      'order_payment_account_number': orderPaymentAccountNumber,
      'order_payment_transfer_number': orderPaymentTransferNumber,
      'order_payment_transfer_company': orderPaymentTransferCompany,
      'order_payment_status': orderPaymentStatus,
      'order_status': orderStatus,
      'order_status_string': orderStatusString,
      'order_status_update_date': orderStatusUpdateDate,
      'order_delivery_type': orderDeliveryType,
      'order_delivery_time_id': orderDeliveryTimeId,
      'order_delivery_time': orderDeliveryTime,
      'order_date': orderDate,
      'order_notes': orderNotes,
      'order_total_price': orderTotalPrice,
      'order_full_total_price': orderFullTotalPrice,
      'order_delivery_fees': orderDeliveryFees,
      'order_items': products.map((e) => e.toJson()).toList(),
      'order_CurrencyVar': orderCurrencyVar
    };
  }
}
