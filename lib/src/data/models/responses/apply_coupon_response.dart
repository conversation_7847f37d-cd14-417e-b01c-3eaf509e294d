class ApplyCouponResponse {
  ApplyCouponResponse({
    required this.status,
    this.result,
    this.message,
  });

  int status;
  ApplyCouponResult? result;
  String? message;

  // from json
  factory ApplyCouponResponse.fromJson(Map<String, dynamic> json) {
    return ApplyCouponResponse(
      status: json['status'] as int,
      message: json['message'] as String?,
      result: json['result'] == null
          ? null
          : ApplyCouponResult.fromJson(json['result']),
    );
  }
}

class ApplyCouponResult {
  ApplyCouponResult(
      {required this.totalAmount,
      required this.totalAmountCurrencyVar,
      required this.deliveryFees,
      required this.deliveryFeesCurrencyVar,
      required this.totalMinOrderPriceAllowUse,
      required this.totalMinOrderPriceAllowUseCurrencyVar,
      required this.discount,
      required this.discountLabel,
      required this.type});

  double totalAmount;
  String totalAmountCurrencyVar;
  double deliveryFees;
  String deliveryFeesCurrencyVar;
  double totalMinOrderPriceAllowUse;
  String totalMinOrderPriceAllowUseCurrencyVar;
  double discount;
  String discountLabel;
  int type;
  // from json
  factory ApplyCouponResult.fromJson(Map<String, dynamic> json) {
    return ApplyCouponResult(
      totalAmount: double.parse(json['total_amount'].toString()),
      totalAmountCurrencyVar: json['total_amount_CurrencyVar'].toString(),
      deliveryFees: double.parse(json['delivery_fees'].toString()),
      deliveryFeesCurrencyVar: json['delivery_fees_CurrencyVar'].toString(),
      totalMinOrderPriceAllowUse:
          double.parse(json['TotalMinOrderPriceAllowUse'].toString()),
      totalMinOrderPriceAllowUseCurrencyVar:
          json['TotalMinOrderPriceAllowUse_CurrencyVar'].toString(),
      discount: double.parse(json['discount'].toString()),
      discountLabel: json['discount_label'].toString(),
      type: int.parse(json['order_coupon_type'].toString()),
    );
  }
}
