import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:matjari/src/controllers/home_controller.dart';
import 'package:matjari/src/core/utils/extenstions/double_extenstion.dart';
import 'package:matjari/src/data/models/order.dart';
import 'package:matjari/src/view/components/form_fields/coupon_form_field.dart';

import '../../../core/values/app_styles.dart';
import '../../../core/values/colors.dart';
import '../../../data/models/hive/cart_item.dart';

class OrderProductList extends StatelessWidget {
  final List<CartItem> items;
  final EdgeInsetsGeometry? margin;
  final bool withCoupon;
  final double deliveryFees;
  final String? orderCurrencyVar;
  final double orderFullTotalPrice;
  final double orderTotalPrice;
  final Order order;
  const OrderProductList({
    super.key,
    required this.items,
    this.margin,
    this.withCoupon = false,
    required this.deliveryFees,
    this.orderCurrencyVar,
    required this.orderFullTotalPrice,
    required this.orderTotalPrice,
    required this.order,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      decoration: BoxDecoration(
        color: AppColors.greyColor,
        borderRadius: AppStyles.cardBorderRadius,
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(18.0),
            child: Row(
              children: [
                Expanded(
                  flex: 5,
                  child: Text(
                    'item'.tr,
                    textAlign: TextAlign.start,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Get.theme.colorScheme.secondary,
                    ),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    'quantity'.tr,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Get.theme.colorScheme.secondary,
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'amount'.tr,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Get.theme.colorScheme.secondary,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const Divider(
            height: 1,
            thickness: 1,
            color: Colors.white,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8),
            child: Column(
              children: [
                ...items.map(
                  (item) {
                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 10.0),
                      child: Row(
                        children: [
                          Expanded(
                            flex: 5,
                            child: Text(
                              item.product.name,
                              style: const TextStyle(
                                  height: 1.5,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 13),
                            ),
                          ),
                          Expanded(
                            flex: 1,
                            child: Text(
                              item.quantity.toString(),
                              textAlign: TextAlign.center,
                              style: const TextStyle(
                                color: Colors.grey,
                              ),
                            ),
                          ),
                          Expanded(
                            flex: 2,
                            child: Text(
                              "${item.totlePrice?.toStringValue() ?? 0} ${item.product.itemCurrencyVar ?? ''}",
                              textAlign: TextAlign.end,
                              style: const TextStyle(
                                color: Colors.grey,
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
          const Divider(
            height: 1,
            thickness: 1,
            color: Colors.white,
          ),
          if (withCoupon) const CouponFormField(),
          // total price
          Padding(
            padding: const EdgeInsets.only(
                top: 16.0, right: 16, left: 16, bottom: 8),
            child: Row(
              children: [
                Expanded(
                  flex: 5,
                  child: Text(
                    'total'.tr,
                    textAlign: TextAlign.start,
                    style: const TextStyle(
                        fontWeight: FontWeight.bold, fontSize: 13),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    '${orderTotalPrice.toStringValue()} ${orderCurrencyVar ?? ''}',
                    textAlign: TextAlign.end,
                    style: const TextStyle(
                      color: Colors.grey,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // delivery price
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 16),
            child: Row(
              children: [
                Expanded(
                  flex: 5,
                  child: Text(
                    "deliveryFees".tr,
                    textAlign: TextAlign.start,
                    style: const TextStyle(
                        fontWeight: FontWeight.bold, fontSize: 13),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    "${deliveryFees.toStringValue()}  ${orderCurrencyVar ?? ''}",
                    textAlign: TextAlign.end,
                    style: const TextStyle(
                      color: Colors.grey,
                    ),
                  ),
                ),
              ],
            ),
          ),
          if (order.orderCouponDiscountAmountCurrencyVar != null)
            // discount
            Padding(
              padding:
                  const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16),
              child: Row(
                children: [
                  Expanded(
                    flex: 5,
                    child: Text(
                      'discount'.tr,
                      textAlign: TextAlign.start,
                      style: TextStyle(
                          color: AppColors.redColor,
                          fontWeight: FontWeight.bold,
                          fontSize: 13),
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      order.orderCouponDiscountAmountCurrencyVar ?? "0.0",
                      textAlign: TextAlign.end,
                      style: TextStyle(
                        color: AppColors.redColor,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          // total price
          Padding(
            padding:
                const EdgeInsets.only(right: 16, left: 16, bottom: 16, top: 8),
            child: Row(
              children: [
                Expanded(
                  flex: 5,
                  child: Text(
                    'order total'.tr,
                    textAlign: TextAlign.start,
                    style: const TextStyle(
                        fontWeight: FontWeight.bold, fontSize: 13),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    '${orderFullTotalPrice.toStringValue()} ${orderCurrencyVar ?? ''}',
                    textAlign: TextAlign.end,
                    style: TextStyle(
                        color: Get.theme.primaryColor,
                        fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
