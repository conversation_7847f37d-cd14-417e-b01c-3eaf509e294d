import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:matjari/src/controllers/cart_controller.dart';
import 'package:matjari/src/controllers/home_controller.dart';
import 'package:matjari/src/core/utils/extenstions/double_extenstion.dart';
import 'package:matjari/src/data/providers/local/hive_provider.dart';
import 'package:matjari/src/data/vms/apply_coupon_vm.dart';
import 'package:matjari/src/view/components/form_fields/coupon_form_field.dart';

import '../../../core/values/app_styles.dart';
import '../../../core/values/colors.dart';
import '../../../data/models/hive/cart_item.dart';



class CheckoutOrderProductList extends StatefulWidget {
  final List<CartItem> items;
  final EdgeInsetsGeometry? margin;
  final bool withCoupon;
  final double deliveryFees;

  final void Function(String)? onCouponApply;

  final void Function(String?)? onCouponSave;

  const CheckoutOrderProductList({
    super.key,
    required this.items,
    this.margin,
    this.withCoupon = false,
    required this.deliveryFees,
    this.onCouponApply,
    this.onCouponSave,
  });

  @override
  State<CheckoutOrderProductList> createState() =>
      _CheckoutOrderProductListState();
}

class _CheckoutOrderProductListState extends State<CheckoutOrderProductList> {
  late double deliveryFeesNumber;
  String deliveryFees = '';
  String totalPrice = '';
  String discount = '';

  double discountNumber = 0.0;

  final couponController = TextEditingController();
  @override
  void initState() {
    defaultValues();
    super.initState();
  }

  void defaultValues() {
    deliveryFeesNumber = widget.deliveryFees;
    deliveryFees =
        "${widget.deliveryFees.toStringValue()}  ${HomeController.instance.currencyCode}";
    totalPrice =
        "${(widget.items.fold<double>(0.0, (total, item) => total + item.total) + (widget.deliveryFees)).toStringValue()} ${HomeController.instance.currencyCode}";

    discount = "0.0 ${HomeController.instance.currencyCode}";
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: widget.margin,
      decoration: BoxDecoration(
        color: AppColors.greyColor,
        borderRadius: AppStyles.cardBorderRadius,
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(18.0),
            child: Row(
              children: [
                Expanded(
                  flex: 5,
                  child: Text(
                    'item'.tr,
                    textAlign: TextAlign.start,
                    style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Get.theme.colorScheme.secondary,
                        fontSize: 14),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'quantity'.tr,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Get.theme.colorScheme.secondary,
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'amount'.tr,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Get.theme.colorScheme.secondary,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const Divider(
            height: 1,
            thickness: 1,
            color: Colors.white,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8),
            child: Column(
              children: [
                ...widget.items.map(
                  (item) {
                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 10.0),
                      child: Row(
                        children: [
                          Expanded(
                            flex: 5,
                            child: Text(
                              item.product.name,
                              style: const TextStyle(
                                  height: 1.5,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 13),
                            ),
                          ),
                          Expanded(
                            flex: 1,
                            child: Text(
                              item.quantity.toString(),
                              textAlign: TextAlign.center,
                              style: const TextStyle(
                                color: Colors.grey,
                              ),
                            ),
                          ),
                          Expanded(
                            flex: 2,
                            child: Text(
                              "${item.total.toStringValue()} ${HomeController.instance.currencyCode}",
                              textAlign: TextAlign.end,
                              style: const TextStyle(
                                color: Colors.grey,
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
          const Divider(
            height: 1,
            thickness: 1,
            color: Colors.white,
          ),
          if (widget.withCoupon)
            CouponFormField(
              couponController: couponController,
              onCouponApply: (coupon) async {
                final cartItems = HiveProvider.allCartItems;

                ApplyCouponVm applyCouponVm = ApplyCouponVm();
                applyCouponVm.coupon = coupon;
                applyCouponVm.adressId =
                    CartController.instance.selectedAddress.value?.addressId;
                applyCouponVm.items = cartItems
                    .map(
                      (e) => {
                        'id': e.product.id,
                        'quantity': e.quantity,
                      },
                    )
                    .toList();
                var result =
                    await CartController.instance.applayCoupon(applyCouponVm);
                if (result != null) {
                  setState(() {
                    deliveryFees = result.deliveryFeesCurrencyVar;
                    deliveryFeesNumber = result.deliveryFees;
                    totalPrice = result.totalAmountCurrencyVar;
                    discount = result.discountLabel;
                    discountNumber = result.discount;
                  });
                  return true;
                } else {
                  couponController.clear();
                }
              },
              onCouponReset: () {
                setState(() {
                  defaultValues();
                });
              },
              onSave: widget.onCouponSave,
            ),
          // total price
          Padding(
            padding: const EdgeInsets.only(
                top: 16.0, right: 16, left: 16, bottom: 8),
            child: Row(
              children: [
                Expanded(
                  flex: 5,
                  child: Text(
                    'total'.tr,
                    textAlign: TextAlign.start,
                    style: const TextStyle(
                        fontWeight: FontWeight.bold, fontSize: 13),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    '${(widget.items.fold<double>(0.0, (total, item) => total + item.total)).toStringValue()} ${HomeController.instance.currencyCode}',
                    textAlign: TextAlign.end,
                    style: const TextStyle(
                      color: Colors.grey,
                    ),
                  ),
                ),
              ],
            ),
          ),
          // delivery price
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 16),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  flex: 5,
                  child: Text(
                    "deliveryFees".tr,
                    textAlign: TextAlign.start,
                    style: const TextStyle(
                        fontWeight: FontWeight.bold, fontSize: 13),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text.rich(
                    TextSpan(
                        // text: deliveryFees,
                        style: const TextStyle(
                          color: Colors.grey,
                        ),
                        children: [
                          TextSpan(
                            text: deliveryFees,
                            style: TextStyle(
                                color: Colors.grey,
                                decoration:
                                    deliveryFeesNumber == (widget.deliveryFees)
                                        ? null
                                        : TextDecoration.lineThrough),
                          ),
                          if (deliveryFeesNumber != (widget.deliveryFees))
                            TextSpan(
                              text: "\n$deliveryFees",
                              style: const TextStyle(
                                color: Colors.grey,
                              ),
                            ),
                        ]),
                    textAlign: TextAlign.end,
                  ),
                ),
              ],
            ),
          ),
          if (widget.withCoupon && discountNumber > 0)
            // discount
            Padding(
              padding:
                  const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16),
              child: Row(
                children: [
                  Expanded(
                    flex: 5,
                    child: Text(
                      'discount'.tr,
                      textAlign: TextAlign.start,
                      style: TextStyle(
                          color: AppColors.redColor,
                          fontWeight: FontWeight.bold,
                          fontSize: 13),
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      discount,
                      textAlign: TextAlign.end,
                      style: TextStyle(
                        color: AppColors.redColor,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          // total price
          Padding(
            padding:
                const EdgeInsets.only(right: 16, left: 16, bottom: 16, top: 8),
            child: Row(
              children: [
                Expanded(
                  flex: 5,
                  child: Text(
                    'order total'.tr,
                    textAlign: TextAlign.start,
                    style: const TextStyle(
                        fontWeight: FontWeight.bold, fontSize: 13),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    totalPrice,
                    textAlign: TextAlign.end,
                    style: TextStyle(
                        color: Get.theme.primaryColor,
                        fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
