import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

import '../../../core/values/colors.dart';
import '../buttons/custom_filled_button.dart';
import 'custom_bottom_sheet.dart';

class ConfirmBottomSheet extends StatelessWidget {
  final String title;
  final String message;
  final String? confirmText;
  final String? cancelText;
  final VoidCallback onConfirm;
  final VoidCallback? onCancel;
  const ConfirmBottomSheet({
    super.key,
    required this.title,
    required this.message,
    required this.onConfirm,
    this.onCancel,
    this.confirmText,
    this.cancelText,
  });

  @override
  Widget build(BuildContext context) {
    return CustomBottomSheet(
      title: title,
      body: Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(
                bottom: 32.0,
                top: 32 - 16,
              ),
              child: Text(message),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: CustomFilledButton(
                    onPressed: onConfirm,
                    child: Text(
                      confirmText ?? 'confirm'.tr,
                    ),
                  ),
                ),
                const SizedBox(
                  width: 16,
                ),
                Expanded(
                  child: CustomFilledButton(
                    backgroundColor: AppColors.greyColor,
                    onPressed: () {
                      if (onCancel != null) {
                        onCancel!();
                      } else {
                        Get.back();
                      }
                    },
                    child: Text(
                      cancelText ?? 'cancel'.tr,
                      style: TextStyle(color: Get.theme.primaryColor),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
