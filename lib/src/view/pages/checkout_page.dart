import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:matjari/src/controllers/about_app_controller.dart';
import 'package:matjari/src/controllers/cart_controller.dart';
import 'package:matjari/src/data/providers/local/hive_provider.dart';
import 'package:matjari/src/data/vms/apply_coupon_vm.dart';
import 'package:matjari/src/data/vms/order_vm.dart';
import 'package:matjari/src/view/components/form_fields/custom_dropdwon_form_field.dart';
import 'package:matjari/src/view/components/form_fields/custom_text_form_field.dart';
import 'package:matjari/src/view/components/form_fields/payment_form_fields.dart';

import 'package:matjari/src/view/components/list_views/checkout_order_product_list.dart';
import 'package:matjari/src/view/components/master_page.dart';

import '../../core/utils/common_functions.dart';
import '../../core/utils/matjari_icons.dart';
import '../../core/utils/validator.dart';

import '../components/buttons/custom_filled_button.dart';

class CheckoutPage extends StatelessWidget {
  const CheckoutPage({super.key});

  @override
  Widget build(BuildContext context) {
    // payment methods
    if (CartController.instance.paymentMethods.value.isEmpty) {
      CartController.instance.getPaymentMethods();
    }
    // delivery times
    if (CartController.instance.deliveryTimes.value.isEmpty) {
      CartController.instance.getDeliveryTimes();
    }

    // form key
    final formKey = GlobalKey<FormState>();
    // order vm
    final orderVM = OrderVM();
    // cart items
    final cartItems = HiveProvider.allCartItems;
    // selected address
    var selectedAddress = CartController.instance.selectedAddress.value;
    orderVM.items = cartItems
        .map(
          (e) => {
            'id': e.product.id,
            'quantity': e.quantity,
          },
        )
        .toList();
    orderVM.adressId = selectedAddress?.addressId ?? 0;
    return MasterPage(
      title: 'checkout'.tr,
      extendBody: false,
      
      resizeToAvoidBottomInset: true,
      body: Form(
        key: formKey,
        autovalidateMode: AutovalidateMode.onUserInteraction,
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(
                        top: 16.0,
                        right: 25,
                        left: 25,
                      ),
                      child: Text(
                        'deliveryAddress'.tr,
                        style: TextStyle(
                          fontSize: 12,
                          color: Get.theme.primaryColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        vertical: 16.0,
                        horizontal: 25,
                      ),
                      child: Row(
                        children: [
                          Icon(
                            MatjariIcons.addresses,
                            color: Get.theme.colorScheme.secondary,
                          ),
                          const SizedBox(
                            width: 16,
                          ),
                          Expanded(
                            child: Text(
                              selectedAddress?.fullAddress ?? "",
                              style: const TextStyle(
                                fontSize: 13,
                                height: 1.5,
                              ),
                            ),
                          )
                        ],
                      ),
                    ),
                    const Divider(
                      height: 1,
                      indent: 16,
                      endIndent: 16,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                        top: 16.0,
                        right: 25,
                        left: 25,
                      ),
                      child: Text(
                        'orderItems'.tr,
                        style: TextStyle(
                          fontSize: 12,
                          color: Get.theme.primaryColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    CheckoutOrderProductList(
                      deliveryFees: CartController
                              .instance.selectedAddress.value?.deliveryFees ??
                          0,
                      withCoupon: AboutAppController
                              .instance.configurations.value?.allowCoupon ??
                          false,
                      margin: const EdgeInsets.all(16),
                      items: HiveProvider.allCartItems,
                      onCouponSave: (value) {
                        orderVM.coupon = value;
                      },
                    ),
                    // Delivery Time
                    Obx(() {
                      var deliveryTimes =
                          CartController.instance.deliveryTimes.value;
                      return CustomDropdownFormField<int>(
                        onTap: () {
                          if (CartController
                              .instance.paymentMethods.value.isEmpty) {
                            CartController.instance.getPaymentMethods();
                          }
                        },
                        margin: const EdgeInsets.only(
                            right: 16, left: 16, bottom: 8),
                        onChanged: (value) {},
                        validator: (value) {
                          return Validator.validate(
                            rules: ["required"],
                            value: value,
                            fieldName: 'deliveryTime'.tr,
                          );
                        },
                        onSave: (value) {
                          if (value != null) {
                            orderVM.deliveryTime = value;
                          }
                        },
                        selectedItemBuilder: (context) =>
                            deliveryTimes.map((e) {
                          return SizedBox(
                            width: Get.width - 100,
                            child: Text(
                              e.timeTitle,
                              overflow: TextOverflow.visible,
                              maxLines: 1,
                              softWrap: false,
                            ),
                          );
                        }).toList(),
                        items: deliveryTimes
                            .map(
                              (e) => DropdownMenuItem(
                                value: e.timeId,
                                child: Text(
                                  e.timeTitle,
                                ),
                              ),
                            )
                            .toList(),
                        titleText: 'deliveryTime'.tr,
                        hintText: 'deliveryTimeHint'.tr,
                      );
                    }),
                    // payment method
                    Obx(() {
                      return PaymentFormFields(
                        paymentMethods:
                            CartController.instance.paymentMethods.value,
                        onSaved: (value) {
                          orderVM.paymentId = value?['payment']?.paymentId ?? 0;
                          orderVM.paymentAccountId =
                              value?['payment_account_id'];
                          orderVM.voucher = value?['voucher'];
                          //
                          orderVM.transferNumber = value?['transferNumber'];
                          orderVM.transferCompany = value?['transferCompany'];
                        },
                      );
                    }),

                    // notes
                    CustomTextFormField(
                      margin: const EdgeInsets.only(
                        right: 16,
                        left: 16,
                        bottom: 16,
                      ),
                      onSave: (value) {
                        orderVM.notes = value ?? "";
                      },
                      titleText: 'notes'.tr,
                      hintText: 'notesHint'.tr,
                      maxLine: 3,
                    ),
                  ],
                ),
              ),
            ),
            CustomFilledButton(
              margin: const EdgeInsets.symmetric(
                vertical: 16,
                horizontal: 16,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text("placeOrder".tr),
                  const Icon(
                    Icons.chevron_right,
                  )
                ],
              ),
              onPressed: () {
                // print(AuthService.instance.deviceId.value);
                // print(AuthService.instance.user.value?.token);
                if (formKey.currentState!.validate()) {
                  formKey.currentState!.save();
                  CommonFunctions.showConfirmBottomSheet(
                    title: "تأكيد الإرسال!",
                    message: "هل أنت متأكد من إرسال الطلب؟",
                    onConfirm: () {
                      Get.back();
                      CartController.instance.addOrder(orderVM);
                    },
                  );
                }
              },
            ),
          ],
        ),
      ),
    );
  }
}
