import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:matjari/src/controllers/about_app_controller.dart';
import 'package:matjari/src/data/models/ads.dart';
import 'package:matjari/src/data/models/currency.dart';
import 'package:matjari/src/data/models/section.dart';
import 'package:matjari/src/data/providers/local/hive_provider.dart';
import 'package:matjari/src/data/services/permission_warning_overlay.dart';

import '../core/routes/app_pages.dart';
import '../core/utils/common_functions.dart';
import '../core/values/app_config.dart';
import '../data/enums/page_status.dart';
import '../data/services/auth_services.dart';
import '../data/services/network_service.dart';

class HomeController extends GetxController {
  // static instance
  static HomeController get instance => Get.find();
  // current page index
  var currentIndex = 0.obs;
  // page view controller
  var pageViewController = PageController(
    initialPage: 0,
  );
  // page status
  var pageStatus = LoadingStatus.loaded.obs;
  // sections
  Rx<List<Section>> sections = Rx<List<Section>>([]);
  // ads
  Rx<List<Ad>> ads = Rx<List<Ad>>([]);

// is show pop up done for ads
  bool showPopUpDone = false;

  //min ordre price
  double? minOrderPrice;

  //app selected currency
  // Currency? get selectedCurrency => HiveProvider.getSelectedCurrency();
  //main currency code
  late String currencyCode;
  //main
  //android app build number
  int? androidBuildNumber;
  //ios app build number
  int? iosBuildNumber;

  //android app force update
  int? androidForceUpdate;
  //ios app force update
  int? iosForceUpdate;

  Future<void> getHomeData({bool withLoading = true}) async {
    if (withLoading) {
      pageStatus.value = LoadingStatus.loading;
    }
    await NetworkService.instance.checkConnectivity(() async {
      try {
        var response = await AppConfigs.apiProvider.getHomePageData();
        if (response.status == 0) {
          androidForceUpdate = response.result!.androidForceUpdae;
          androidBuildNumber = response.result!.androidBuildNumber;
          iosForceUpdate = response.result!.iosForceUpdae;
          iosBuildNumber = response.result!.iosBuildNumber;
          ads.value = response.result!.ads;
          sections.value = response.result!.sections;
          minOrderPrice = response.result!.minOrderPrice;
          currencyCode = response.result!.mainCurrencyCode;
          pageStatus.value = LoadingStatus.loaded;
        } else if (response.status == 2) {
          pageStatus.value = LoadingStatus.error;
          CommonFunctions.showErrorMessage(response.message ?? "");
          Get.toNamed(Routes.LOGIN_PAGE);
        } else {
          CommonFunctions.showErrorMessage("حصل خطأ أثناء جلب البيانات");
        }
      } catch (e) {
        pageStatus.value = LoadingStatus.error;
      }
    }, () {
      pageStatus.value = LoadingStatus.networkError;
    });
  }

  @override
  void onInit() async {
    await getHomeData();

    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();

     //show permission warning
    PermissionWarningOverlayService.instance.showPermissionWarning();

    ever(HomeController.instance.sections, (callback) {
      //show upgrade app if the app is up to date then call the this callback
      showUpdateDialog(() {
        if (AuthService.instance.user.value != null &&
            (AuthService.instance.user.value?.name == null ||
                AuthService.instance.user.value?.name == "")) {
          CommonFunctions.showProfileComplatenessAlertDialog();
        } else {
          var ad = HomeController.instance.ads.value
              .firstWhereOrNull((element) => element.adType == 2);

          //show Popup ads

          if (ad != null) {
            CommonFunctions.showPopUpAd(ad);

            HomeController.instance.showPopUpDone = true;
          }
        }
      });
    });
  }

  showUpdateDialog(VoidCallback onAppUpToDate) {
    var isAndroid = Platform.isAndroid;
    switch (isAndroid) {
      case true:
        showAndroidUpdateDialog(onAppUpToDate);
        break;
      case false:
        showIosUpdateDialog(onAppUpToDate);
        break;
      default:
        break;
    }
  }

//android upgrade dialog
  showAndroidUpdateDialog(VoidCallback onAppUpToDate) {
    int forceUpdate = HomeController.instance.androidForceUpdate ?? 0;

    if (forceUpdate == 1 || forceUpdate == 2) {
      if (!Get.isRegistered<AboutAppController>()) {
        Get.put(AboutAppController());
      }

      var appBuildNumber = AboutAppController.instance.buildnumber;

      if (appBuildNumber < (HomeController.instance.androidBuildNumber ?? 0)) {
        CommonFunctions.upgradeAlert(forceUpdate);
      } else {
        onAppUpToDate();
      }
    } else {
      onAppUpToDate();
    }
  }

//ios upgrade dialog
  showIosUpdateDialog(VoidCallback onAppUpToDate) {
    int forceUpdate = HomeController.instance.iosForceUpdate ?? 0;
    if (forceUpdate == 1 || forceUpdate == 2) {
      if (!Get.isRegistered<AboutAppController>()) {
        Get.put(AboutAppController());
      }

      var appBuildNumber = AboutAppController.instance.buildnumber;

      if (appBuildNumber < (HomeController.instance.iosBuildNumber ?? 0)) {
        CommonFunctions.upgradeAlert(forceUpdate);
      } else {
        onAppUpToDate();
      }
    } else {
      onAppUpToDate();
    }
  }
}
